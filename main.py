from services.bot import TradingBot
from dotenv import load_dotenv
import asyncio

load_dotenv()


async def main():
    try:
        bot = TradingBot()
        await bot.run()
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"<PERSON><PERSON> failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
