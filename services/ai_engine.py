import logging
from typing import Optional, Dict, List, Any
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>sonOutputParser
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from utils.google_news import GoogleNews
import utils.fear_and_greed_utils as fg
import time
from datetime import datetime, timedelta

logger = logging.getLogger()


# Define the desired JSON structure for the AI's response for a NEW entry
class EntryAnalysis(BaseModel):
    market_sentiment: str = Field(
        description="Options: 'Very Bullish', 'Bullish', 'Neutral', 'Bearish', 'Very Bearish'"
    )
    volatility_outlook: str = Field(description="Options: 'High', 'Medium', 'Low'")
    confidence_score: float = Field(
        description="A float between 0.0 and 1.0 for entering a new trade."
    )


# Define the desired JSON structure for the AI's response for an OPEN position
class PositionAnalysis(BaseModel):
    action: str = Field(description="Recommended action. Options: 'hold', 'sell_now'")
    reasoning: str = Field(
        description="A brief explanation for the recommended action."
    )
    new_take_profit_percentage: Optional[float] = Field(
        None,
        description="Suggest a new take-profit percentage based on market conditions.",
    )
    new_trailing_stop_deviation_percentage: Optional[float] = Field(
        None, description="Suggest a new trailing stop deviation based on volatility."
    )


class AIEngine:
    def __init__(self, config, google_news: GoogleNews):
        self.config = config
        self.llm = ChatOpenAI(
            temperature=0, model="gpt-5-mini-2025-08-07"
        )  # Real implementation
        self.entry_parser = JsonOutputParser(pydantic_object=EntryAnalysis)
        self.position_parser = JsonOutputParser(pydantic_object=PositionAnalysis)
        self.entry_prompt = self._create_entry_prompt_template()
        self.position_prompt = self._create_position_prompt_template()
        self.news_fetcher = google_news

        # Context management
        self.max_context_length = 3000  # Maximum characters for context
        self.context_cache = {}
        self.cache_duration = 300  # 5 minutes cache

    def _create_entry_prompt_template(self):
        prompt_str = """
        You are an expert crypto market analyst for a trading bot. Your role is to provide a structured assessment of the market conditions for {trading_pair} to decide if it's a good time to enter a NEW trade. Do not give trading advice, only analyze the data.

        **Format Instructions:**
        {format_instructions}

        **Market Data:**
        - Recent News: {news_headlines}
        - Social Media Sentiment Score: {sentiment_score}
        - Technical Indicator (RSI 4h): {rsi}

        Provide your analysis based *only* on the data above.
        """
        return ChatPromptTemplate.from_template(
            template=prompt_str,
            partial_variables={
                "format_instructions": self.entry_parser.get_format_instructions()
            },
        )

    def _create_position_prompt_template(self):
        prompt_str = """
        You are an expert crypto market analyst for a trading bot. You are analyzing an OPEN position for {trading_pair}. Your role is to recommend an action and adjust parameters based on new data.

        **Format Instructions:**
        {format_instructions}

        **Open Position Details:**
        - Average Entry Price: {average_price}
        - Current Profit/Loss: {pnl_percentage}%
        - Number of DCA Orders: {dca_orders}

        **New Market Data:**
        - Current Price: {current_price}
        - Recent News: {news_headlines}
        - Social Media Sentiment Score: {sentiment_score}
        - Technical Indicator (RSI 4h): {rsi}

        Based on the new data, should the bot hold the position, or sell now? Provide your reasoning and suggest adjusted parameters if holding. For example, if the market becomes more bullish, you might increase the take-profit target. If volatility increases, you might tighten the trailing stop.
        """
        return ChatPromptTemplate.from_template(
            template=prompt_str,
            partial_variables={
                "format_instructions": self.position_parser.get_format_instructions()
            },
        )

    def analyze_for_new_entry(self, rsi: float):
        """Queries the LLM for analysis on whether to enter a new trade."""
        if not self.config["ai_enabled"]:
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

        logger.info("Querying AI for new entry analysis...")

        # Get market context with caching and truncation
        market_context = self._get_market_context()

        # Prepare market data
        market_data = {"trading_pair": self.config["trading_pair"], "rsi": rsi}

        # Add market context
        market_data.update(market_context)

        chain = self.entry_prompt | self.llm | self.entry_parser
        try:
            result = chain.invoke(market_data)
            favorable_sentiments = ["Bullish", "Very Bullish"]
            result["is_entry_favorable"] = (
                result["market_sentiment"] in favorable_sentiments
                and result["confidence_score"] > 0.6
            )
            logger.info(f"AI Entry Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for entry analysis: {e}")
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

    def analyze_open_position(self, position, current_price, rsi: float):
        """Queries the LLM for analysis on an existing trade."""
        if not self.config["ai_enabled"]:
            return {"action": "hold", "reasoning": "AI disabled."}

        current_price = float(current_price)
        logger.info("Querying AI for open position analysis...")

        pnl_percentage = (
            (current_price - position["average_price"]) / position["average_price"]
        ) * 100

        # Get market context with caching and truncation
        market_context = self._get_market_context()

        # Prepare market data
        market_data = {
            "trading_pair": self.config["trading_pair"],
            "average_price": position["average_price"],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "dca_orders": len(position["dca_orders"]),
            "current_price": current_price,
            "rsi": rsi,
        }

        # Add market context
        market_data.update(market_context)

        chain = self.position_prompt | self.llm | self.position_parser
        try:
            result = chain.invoke(market_data)
            logger.info(f"AI Position Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for position analysis: {e}")
            return {"action": "hold", "reasoning": "AI call failed."}

    def _truncate_context(self, text: str, max_length: int = None) -> str:
        """Truncate text to avoid context overflow while preserving important information."""
        if max_length is None:
            max_length = self.max_context_length

        if len(text) <= max_length:
            return text

        # Try to truncate at sentence boundaries
        sentences = text.split(". ")
        truncated = ""

        for sentence in sentences:
            if len(truncated + sentence + ". ") <= max_length - 50:  # Leave some buffer
                truncated += sentence + ". "
            else:
                break

        if not truncated:  # If no complete sentences fit, just truncate
            truncated = text[: max_length - 3] + "..."
        else:
            truncated += "..."

        return truncated

    def _get_cached_context(self, cache_key: str) -> Optional[Dict]:
        """Get cached context if still valid."""
        if cache_key in self.context_cache:
            cached_data, timestamp = self.context_cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
            else:
                # Remove expired cache
                del self.context_cache[cache_key]
        return None

    def _cache_context(self, cache_key: str, data: Dict):
        """Cache context data with timestamp."""
        self.context_cache[cache_key] = (data, time.time())

    def _get_market_context(
        self, include_news: bool = True, include_sentiment: bool = True
    ) -> Dict:
        """Get comprehensive market context with caching and truncation."""
        cache_key = f"market_context_{include_news}_{include_sentiment}"

        # Try to get from cache first
        cached = self._get_cached_context(cache_key)
        if cached:
            logger.debug("Using cached market context")
            return cached

        context = {}

        # Get news headlines
        if include_news:
            try:
                query = '("cryptocurrency" OR "crypto" OR "blockchain" OR "bitcoin" OR "ethereum" OR "altcoin" OR "DeFi" OR "NFT" OR "stablecoin") AND ("price" OR "market" OR "regulation" OR "update" OR "analysis" OR "trading" OR "news")'
                news_headlines = self.news_fetcher.get_news(query)
                if news_headlines:
                    # Truncate news to avoid overflow
                    news_text = str(news_headlines)
                    context["news_headlines"] = self._truncate_context(news_text, 800)
                else:
                    context["news_headlines"] = "No recent news available"
            except Exception as e:
                logger.error(f"Error fetching news: {e}")
                context["news_headlines"] = "Error fetching news"

        # Get sentiment data
        if include_sentiment:
            try:
                fear_greed = fg.get_fear_and_greed_index()
                if fear_greed:
                    context["sentiment_score"] = (
                        f"{fear_greed.get('value', 'N/A')} ({fear_greed.get('value_classification', 'Unknown')})"
                    )
                else:
                    context["sentiment_score"] = "Sentiment data unavailable"
            except Exception as e:
                logger.error(f"Error fetching sentiment: {e}")
                context["sentiment_score"] = "Error fetching sentiment"

        # Cache the context
        self._cache_context(cache_key, context)

        return context

    def analyze_startup_position(
        self, position: Dict, current_price: float, rsi: float
    ) -> Dict:
        """Analyze existing position when bot starts up."""
        if not self.config["ai_enabled"]:
            return {"action": "hold", "reasoning": "AI disabled."}

        logger.info("Analyzing existing position at startup...")

        current_price = float(current_price)
        pnl_percentage = (
            (current_price - position["average_price"]) / position["average_price"]
        ) * 100

        # Get market context with truncation
        market_context = self._get_market_context()

        # Calculate position metrics
        holding_time_hours = 0
        if position.get("last_ai_check_ts"):
            holding_time_hours = (time.time() - position["last_ai_check_ts"]) / 3600

        # Prepare market data with context management
        market_data = {
            "trading_pair": self.config["trading_pair"],
            "average_price": position["average_price"],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "dca_orders": len(position["dca_orders"]),
            "current_price": current_price,
            "holding_time_hours": f"{holding_time_hours:.1f}",
            "rsi": str(rsi),
            "startup_analysis": "true",
        }

        # Add market context
        market_data.update(market_context)

        # Enhanced position prompt for startup
        startup_prompt_str = """
        You are analyzing an EXISTING position at bot startup for {trading_pair}.
        This position was opened previously and you need to assess its current status.

        **Format Instructions:**
        {format_instructions}

        **Position Status:**
        - Entry Price: ${average_price}
        - Current Price: ${current_price}
        - Current P&L: {pnl_percentage}%
        - DCA Orders Used: {dca_orders}
        - Holding Time: {holding_time_hours} hours
        - RSI: {rsi}

        **Market Context:**
        - Recent News: {news_headlines}
        - Market Sentiment: {sentiment_score}

        **Special Instructions for Startup Analysis:**
        - Consider if market conditions have changed significantly
        - Evaluate if the position still aligns with current market sentiment
        - Be more conservative with sell recommendations at startup
        - Consider the holding time when making recommendations

        Provide your analysis based on the current market conditions and position status.
        """

        startup_prompt = ChatPromptTemplate.from_template(
            template=startup_prompt_str,
            partial_variables={
                "format_instructions": self.position_parser.get_format_instructions()
            },
        )

        chain = startup_prompt | self.llm | self.position_parser

        try:
            result = chain.invoke(market_data)
            result["startup_analysis"] = True
            logger.info(f"Startup position analysis completed: {result}")
            return result
        except Exception as e:
            logger.error(f"Error in startup position analysis: {e}")
            return {
                "action": "hold",
                "reasoning": "Startup analysis failed, holding position.",
            }

    def get_context_summary(self) -> Dict:
        """Get a summary of current context cache status."""
        return {
            "cached_items": len(self.context_cache),
            "cache_duration": self.cache_duration,
            "max_context_length": self.max_context_length,
            "cache_keys": list(self.context_cache.keys()),
        }
